import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:file_picker/file_picker.dart';
import 'package:provider/provider.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:dio/dio.dart';
import 'package:http_parser/http_parser.dart';
import '../config/colors.dart';
import '../config/models/constants.dart';
import '../config/theme_helper.dart';
import '../config/app_localizations.dart';
import '../widgets/locale_provider.dart';

class RenewRequestScreen extends StatefulWidget {
  final int permitId; // Dynamic permit ID
  final Map<String, dynamic>? permitData; // Optional permit data for validation
  const RenewRequestScreen({super.key, this.permitId = 1, this.permitData}); // Default to 1 for testing

  @override
  _RenewRequestScreenState createState() => _RenewRequestScreenState();
}

class _RenewRequestScreenState extends State<RenewRequestScreen> {
  PlatformFile? _selectedFile;
  final _formKey = GlobalKey<FormState>();
  final _storage = const FlutterSecureStorage();
  final TextEditingController _newExpiryDateController = TextEditingController();
  bool _isLoading = false;
  bool _isValidatingPermit = false;
  String? _errorMessage;
  DateTime? _selectedDate;

  final Dio _dio = Dio()
    ..interceptors.add(InterceptorsWrapper(
      onError: (DioException e, handler) {
        print('Dio Error: ${e.message}, Response: ${e.response?.data}');
        return handler.next(e);
      },
    ));

  @override
  void initState() {
    super.initState();
    _validatePermitEligibility();
  }

  @override
  void dispose() {
    _newExpiryDateController.dispose();
    super.dispose();
  }

  /// Get authentication token from secure storage
  Future<String?> _getAuthToken() async {
    try {
      final token = await _storage.read(key: 'auth_token');
      if (token == null) {
        print('No auth token found in FlutterSecureStorage');
      }
      return token;
    } catch (e) {
      print('Error reading auth token from FlutterSecureStorage: $e');
      return null;
    }
  }

  /// Validate permit eligibility for renewal
  Future<void> _validatePermitEligibility() async {
    if (!mounted) return;

    setState(() {
      _isValidatingPermit = true;
      _errorMessage = null;
    });

    try {
      final token = await _getAuthToken();
      if (token == null) {
        setState(() {
          _errorMessage = AppLocalizations.tr(context, 'auth_token_missing') ?? 'Authentication token is missing';
          _isValidatingPermit = false;
        });
        return;
      }

      // Fetch permit details to validate ownership and expiry
      final response = await http.get(
        Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/permits/${widget.permitId}'),
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      );

      if (!mounted) return;

      if (response.statusCode == 200) {
        final permitData = jsonDecode(response.body);
        // Additional validation can be added here based on API response structure
        print('Permit validation successful: ${response.body}');
      } else if (response.statusCode == 403) {
        setState(() {
          _errorMessage = 'You can only renew your own permits';
        });
      } else {
        setState(() {
          _errorMessage = 'Failed to validate permit eligibility';
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error validating permit: $e';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isValidatingPermit = false;
        });
      }
    }
  }

  Future<void> _pickFile() async {
    if (!mounted) return;

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
        withData: true,
      );

      if (!mounted) return;

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.size > 5 * 1024 * 1024) { // 5MB limit
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.tr(context, 'file_too_large')),
              backgroundColor: AppColors.error,
            ),
          );
          return;
        }
        print('Selected File: ${file.name}, Size: ${file.size} bytes');
        setState(() {
          _selectedFile = file;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.tr(context, 'file_selected_success')),
            backgroundColor: AppColors.success,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.tr(context, 'no_file_selected')),
            backgroundColor: AppColors.warning,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      print('File Picker Error: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${AppLocalizations.tr(context, 'file_pick_error')}: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _submitForm() async {
    if (_selectedFile == null || _selectedFile!.bytes == null) {
      setState(() {
        _errorMessage = AppLocalizations.tr(context, 'no_file_uploaded');
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.tr(context, 'no_file_uploaded')),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    if (_selectedDate == null) {
      setState(() {
        _errorMessage = 'Veuillez sélectionner une nouvelle date d\'expiration';
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Veuillez sélectionner une nouvelle date d\'expiration'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final token = await _getAuthToken();
      if (token == null || token.isEmpty) {
        setState(() {
          _errorMessage = AppLocalizations.tr(context, 'not_authenticated');
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.tr(context, 'not_authenticated')),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }

      // Format the date as YYYY-MM-DD for the backend
      final formattedDate = '${_selectedDate!.year.toString().padLeft(4, '0')}-${_selectedDate!.month.toString().padLeft(2, '0')}-${_selectedDate!.day.toString().padLeft(2, '0')}';

      final formData = FormData.fromMap({
        'newExpiryDate': formattedDate,
        'pdf': MultipartFile.fromBytes(
          _selectedFile!.bytes!,
          filename: _selectedFile!.name,
          contentType: MediaType('application', 'pdf'),
        ),
      });

      print('Sending formData: ${formData.fields}'); // Debug log

      final response = await _dio.post(
        'https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/permits/${widget.permitId}/renew',
        data: formData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'multipart/form-data',
            'Accept': 'application/json',
          },
          validateStatus: (status) => status! < 500,
          receiveTimeout: const Duration(seconds: 30),
          sendTimeout: const Duration(seconds: 30),
        ),
      );

      print('Response: ${response.statusCode}, ${response.data}'); // Debug log

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data;
        if (responseData is Map && responseData['success'] == true) {
          await Future.delayed(const Duration(milliseconds: 100));
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.tr(context, 'form_submitted_success')),
              backgroundColor: AppColors.success,
            ),
          );
          Navigator.of(context).pop();
        } else {
          setState(() {
            _errorMessage = AppLocalizations.tr(context, 'no_response_data') +
                ': ${response.data.toString()}';
            _isLoading = false;
          });
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_errorMessage!),
              backgroundColor: AppColors.error,
            ),
          );
        }
      } else {
        String errorMsg = AppLocalizations.tr(context, 'submission_failed');
        if (response.data is Map) {
          if (response.data['message'] != null) {
            errorMsg = response.data['message'].toString();
            if (response.data['errors'] != null) {
              errorMsg += ': ${response.data['errors'].toString()}';
            }
          } else if (response.statusCode == 401) {
            errorMsg = AppLocalizations.tr(context, 'invalid_token');
          } else if (response.statusCode == 422) {
            errorMsg = AppLocalizations.tr(context, 'invalid_form_data');
            if (response.data['errors'] != null) {
              errorMsg += ': ${response.data['errors'].toString()}';
            }
          } else if (response.statusCode == 400) {
            errorMsg = AppLocalizations.tr(context, 'bad_request');
          }
        }
        setState(() {
          _errorMessage = '$errorMsg (Status: ${response.statusCode})';
          _isLoading = false;
        });
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } on DioException catch (e) {
      print('DioException: ${e.response?.statusCode}, ${e.response?.data}, ${e.requestOptions.uri}');
      String errorMsg = AppLocalizations.tr(context, 'network_error');
      if (e.response?.data is Map) {
        errorMsg = e.response!.data['message']?.toString() ?? errorMsg;
        if (e.response!.data['errors'] != null) {
          errorMsg += ': ${e.response!.data['errors'].toString()}';
        }
      }
      setState(() {
        _errorMessage = errorMsg;
        _isLoading = false;
      });
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_errorMessage!),
          backgroundColor: AppColors.error,
        ),
      );
    } catch (e) {
      print('Unexpected Error: $e, StackTrace: ${StackTrace.current}');
      setState(() {
        _errorMessage = '${AppLocalizations.tr(context, 'network_error')}: $e';
        _isLoading = false;
      });
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_errorMessage!),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';
    final colors = ThemeHelper.getColorsWithListener(context);

    return Scaffold(
      backgroundColor: colors.backgroundPrimary,
      appBar: AppBar(
        title: Text(
          AppLocalizations.tr(context, 'renew_request'),
          style: ThemeHelper.getTitleStyle(context),
        ),
        backgroundColor: AppColors.primaryOrange,
        foregroundColor: AppColors.pureWhite,
        elevation: 0,
      ),
      body: _isValidatingPermit
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? _buildErrorWidget()
              : Form(
                  key: _formKey,
                  child: SingleChildScrollView(
                    padding: Constants.screenPadding,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.tr(context, 'renew_request'),
                          style: ThemeHelper.getSectionTitleStyle(context),
                        ),
                        const SizedBox(height: Constants.mediumSpacing),
                        Card(
                          elevation: Constants.cardTheme.elevation,
                          shape: Constants.cardTheme.shape,
                          color: ThemeHelper.getColors(context).card,
                          child: Padding(
                            padding: Constants.cardPadding,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildRequiredDocumentsList(isArabic),
                                const SizedBox(height: Constants.largeSpacing),
                                _buildFileUploadSection(isArabic),
                                const SizedBox(height: Constants.largeSpacing),
                                _buildNewExpiryDateField(isArabic),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: Constants.extraLargeSpacing),
                        _buildActionButton(isArabic, context),
                      ],
                    ),
                  ),
                ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: Constants.screenPadding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: Constants.mediumSpacing),
            Text(
              _errorMessage!,
              style: ThemeHelper.getSubtitleStyle(context).copyWith(
                color: AppColors.error,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: Constants.largeSpacing),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _errorMessage = null;
                });
                _validatePermitEligibility();
              },
              style: ThemeHelper.getPrimaryButtonStyle(context),
              child: Text(AppLocalizations.tr(context, 'retry') ?? 'Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRequiredDocumentsList(bool isArabic) {
    final documents = [
      AppLocalizations.tr(context, 'document_id_copy'),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: documents.map((doc) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            child: Row(
              textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                    top: 6,
                    left: isArabic ? 8 : 0,
                    right: isArabic ? 0 : 8,
                  ),
                  child: Container(
                    width: 6,
                    height: 6,
                    decoration: const BoxDecoration(
                      color: AppColors.info,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    doc,
                    style: ThemeHelper.getSubtitleStyle(context),
                    textAlign: isArabic ? TextAlign.right : TextAlign.left,
                  ),
                ),
              ],
            ),
          )).toList(),
    );
  }

  Widget _buildFileUploadSection(bool isArabic) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const Icon(
          Icons.upload_file,
          size: 48,
          color: AppColors.info,
        ),
        const SizedBox(height: Constants.mediumSpacing),
        Text(
          AppLocalizations.tr(context, 'upload_file'),
          style: ThemeHelper.getSectionTitleStyle(context).copyWith(fontSize: 18),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          AppLocalizations.tr(context, 'click_to_select_file'),
          style: ThemeHelper.getSubtitleStyle(context),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: Constants.mediumSpacing),
        Center(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _pickFile,
            style: ThemeHelper.getSecondaryButtonStyle(context).copyWith(
              minimumSize: WidgetStateProperty.all(const Size(200, 40)),
            ),
            child: Text(AppLocalizations.tr(context, 'select_file_button')),
          ),
        ),
        if (_selectedFile != null) ...[
          const SizedBox(height: Constants.mediumSpacing),
          Center(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ThemeHelper.getColors(context).backgroundSecondary,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    AppLocalizations.tr(context, 'selected_file'),
                    style: ThemeHelper.getSubtitleStyle(context).copyWith(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.picture_as_pdf,
                        size: 20,
                        color: AppColors.darkGray,
                      ),
                      const SizedBox(width: 8),
                      Flexible(
                        child: Text(
                          _selectedFile!.name,
                          style: ThemeHelper.getSubtitleStyle(context),
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, size: 20),
                        onPressed: () => setState(() => _selectedFile = null),
                        color: AppColors.darkGray,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
        const SizedBox(height: 8),
        Text(
          AppLocalizations.tr(context, 'pdf_only'),
          style: ThemeHelper.getSubtitleStyle(context).copyWith(
            fontSize: 12,
            color: ThemeHelper.getColors(context).textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildNewExpiryDateField(bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Nouvelle date d\'expiration',
          style: ThemeHelper.getSubtitleStyle(context).copyWith(fontWeight: FontWeight.bold),
          textAlign: isArabic ? TextAlign.right : TextAlign.left,
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _newExpiryDateController,
          readOnly: true,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            hintText: 'Sélectionner une date',
            suffixIcon: const Icon(Icons.calendar_today),
          ),
          onTap: () async {
            final DateTime? picked = await showDatePicker(
              context: context,
              initialDate: DateTime.now().add(const Duration(days: 30)),
              firstDate: DateTime.now(),
              lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
            );
            if (picked != null) {
              setState(() {
                _selectedDate = picked;
                _newExpiryDateController.text = '${picked.day.toString().padLeft(2, '0')}/${picked.month.toString().padLeft(2, '0')}/${picked.year}';
              });
            }
          },
          validator: (value) {
            if (_selectedDate == null) {
              return 'Veuillez sélectionner une date d\'expiration';
            }
            return null;
          },
        ),
      ],
    );
  }



  Widget _buildActionButton(bool isArabic, BuildContext context) {
    return Center(
      child: ElevatedButton(
        onPressed: (_isLoading || _selectedFiles.isEmpty) ? null : _submitForm,
        style: ThemeHelper.getPrimaryButtonStyle(context).copyWith(
          minimumSize: WidgetStateProperty.all(const Size(200, 40)),
        ),
        child: _isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: AppColors.pureWhite,
                  strokeWidth: 2,
                ),
              )
            : Text(AppLocalizations.tr(context, 'submit')),
      ),
    );
  }
}